/* ===== CSS VARIABLES ===== */
:root {
    --primary-color: #1e3a8a;
    --secondary-color: #3b82f6;
    --accent-color: #f97316;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --bg-light: #f8fafc;
    --bg-dark: #0f172a;
    --white: #ffffff;
    --border-color: #e5e7eb;

    --font-primary: 'Inter', sans-serif;
    --font-display: 'Playfair Display', serif;

    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    --transition: all 0.3s ease;
}

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--white);
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

@media (max-width: 768px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.5rem; }
}

/* ===== UTILITY CLASSES ===== */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition);
}
.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.section__title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.section__subtitle {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
    position: relative;
    z-index: 1000;
}

.navbar {
    background: rgba(15, 23, 42, 0.95) !important;
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar.scrolled {
    background: rgba(15, 23, 42, 0.98) !important;
    box-shadow: var(--shadow-md);
}

/* ===== LOGO STYLES ===== */
.logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-decoration: none;
    color: var(--white) !important;
    font-family: var(--font-display);
}

.logo__text {
    font-size: 1.75rem;
    font-weight: 700;
    line-height: 1;
    color: var(--white);
}

.logo__subtitle {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--accent-color);
    line-height: 1;
    margin-top: 0.25rem;
}

.navbar-brand.logo:hover {
    color: var(--white) !important;
}

/* ===== NAVIGATION LINKS ===== */
.navbar-nav .nav-link {
    color: var(--white) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--primary-color) 100%);
}

.hero__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.1;
    z-index: 1;
}

.hero__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 58, 138, 0.6) 100%);
    z-index: 2;
}

.hero__content {
    position: relative;
    z-index: 3;
    color: var(--white);
}

.hero__title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero__subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
}

.hero__actions {
    margin-bottom: 3rem;
}

.hero__features {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.hero__features .feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--white);
    opacity: 0.9;
}

.hero__features .feature i {
    color: var(--accent-color);
    font-size: 1.25rem;
}

@media (max-width: 768px) {
    .hero__title {
        font-size: 2.5rem;
    }

    .hero__subtitle {
        font-size: 1.125rem;
    }

    .hero__features {
        flex-direction: column;
        gap: 1rem;
    }

    .hero__background {
        background-attachment: scroll;
    }
}

/* ===== SERVICES SECTION ===== */
.services {
    padding: 5rem 0;
    background-color: var(--bg-light);
}

.service-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    height: 100%;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.service-card__icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.service-card__icon i {
    font-size: 2rem;
    color: var(--white);
}

.service-card__title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.service-card__description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.service-card__features {
    list-style: none;
    padding: 0;
}

.service-card__features li {
    padding: 0.5rem 0;
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
}

.service-card__features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: 5rem 0;
    background-color: var(--white);
}

.about__content {
    padding-right: 2rem;
}

.about__description {
    font-size: 1.125rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.about__features {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature-item {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.feature-item__icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--accent-color), #fb923c);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-item__icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.feature-item__content h4 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.feature-item__content p {
    color: var(--text-light);
    margin: 0;
}

.about__image img {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

@media (max-width: 991px) {
    .about__content {
        padding-right: 0;
        margin-bottom: 3rem;
    }
}

/* ===== FAQ SECTION ===== */
.faq {
    padding: 5rem 0;
    background-color: var(--bg-light);
}

.accordion-item {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius) !important;
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-button {
    background-color: var(--white);
    color: var(--text-dark);
    font-weight: 600;
    padding: 1.5rem;
    border: none;
    font-size: 1.125rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--primary-color);
    color: var(--white);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-body {
    padding: 1.5rem;
    background-color: var(--white);
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.6;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--primary-color) 100%);
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.1;
    z-index: 1;
}

.contact .container {
    position: relative;
    z-index: 2;
}

.contact__form {
    background: rgba(255, 255, 255, 0.95);
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
}

.contact__form .form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.contact__form .form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
}

.contact__form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
}

.contact__info {
    text-align: center;
    color: var(--white);
}

.contact__icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    backdrop-filter: blur(10px);
}

.contact__icon i {
    font-size: 2rem;
    color: var(--accent-color);
}

.contact__info h4 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.contact__info p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

.contact__info a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 600;
}

.contact__info a:hover {
    color: var(--white);
}

@media (max-width: 768px) {
    .contact__form {
        padding: 2rem;
    }

    .contact::before {
        background-attachment: scroll;
    }
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--bg-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer__brand .logo {
    margin-bottom: 1rem;
}

.footer__brand p {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

.footer h5 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.footer__links {
    list-style: none;
    padding: 0;
}

.footer__links li {
    margin-bottom: 0.5rem;
}

.footer__links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer__links a:hover {
    color: var(--accent-color);
}

.newsletter-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
}

.newsletter-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.newsletter-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--accent-color);
    box-shadow: none;
    color: var(--white);
}

.footer__divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 2rem 0 1rem;
}

.footer__copyright {
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991px) {
    .hero__title {
        font-size: 3rem;
    }

    .section__title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .hero {
        text-align: center;
    }

    .hero__actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .hero__actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .service-card {
        text-align: center;
    }

    .feature-item {
        text-align: center;
        flex-direction: column;
        align-items: center;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== SCROLL ANIMATIONS ===== */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.scroll-animate.animate {
    opacity: 1;
    transform: translateY(0);
}

/* ===== PARALLAX EFFECT ===== */
.parallax {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

@media (max-width: 768px) {
    .parallax {
        background-attachment: scroll;
    }
}

/* ===== PAGE HEADER ===== */
.page-header {
    padding: 8rem 0 4rem;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--primary-color) 100%);
    color: var(--white);
    text-align: center;
}

.page-header__title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--white);
}

.page-header__subtitle {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .page-header {
        padding: 6rem 0 3rem;
    }

    .page-header__title {
        font-size: 2.5rem;
    }

    .page-header__subtitle {
        font-size: 1.125rem;
    }
}

/* ===== PRICING SECTION ===== */
.pricing {
    padding: 5rem 0;
    background-color: var(--bg-light);
}

.pricing-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    padding: 2.5rem;
    text-align: center;
    transition: var(--transition);
    height: 100%;
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.pricing-card--featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card--featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.pricing-card__badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-color);
    color: var(--white);
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-card__header {
    margin-bottom: 2rem;
}

.pricing-card__icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.pricing-card__icon i {
    font-size: 2rem;
    color: var(--white);
}

.pricing-card__header h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.pricing-card__header p {
    color: var(--text-light);
    margin: 0;
}

.pricing-card__price {
    margin-bottom: 2rem;
}

.pricing-card__price .price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    display: block;
    line-height: 1;
}

.pricing-card__price .period {
    color: var(--text-light);
    font-size: 1rem;
}

.pricing-card__features {
    list-style: none;
    padding: 0;
    text-align: left;
}

.pricing-card__features li {
    padding: 0.75rem 0;
    color: var(--text-light);
    position: relative;
    padding-left: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.pricing-card__features li:last-child {
    border-bottom: none;
}

.pricing-card__features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.125rem;
}

/* ===== SERVICE PRICING ===== */
.service-pricing {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    height: 100%;
}

.service-pricing h4 {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
    color: var(--text-dark);
}

.service-pricing__list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.service-item:hover {
    background: rgba(30, 58, 138, 0.05);
}

.service-name {
    font-weight: 500;
    color: var(--text-dark);
}

.service-price {
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
}

@media (max-width: 768px) {
    .service-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .service-price {
        align-self: flex-end;
    }
}

/* ===== PRICING NOTES ===== */
.pricing-notes {
    padding: 4rem 0;
    background-color: var(--white);
}

.pricing-notes__content {
    background: var(--bg-light);
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--primary-color);
}

.pricing-notes__content h3 {
    color: var(--text-dark);
    margin-bottom: 2rem;
    text-align: center;
}

.pricing-notes__list {
    list-style: none;
    padding: 0;
}

.pricing-notes__list li {
    padding: 0.75rem 0;
    color: var(--text-light);
    position: relative;
    padding-left: 2rem;
}

.pricing-notes__list li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.25rem;
}

.pricing-notes__list strong {
    color: var(--text-dark);
}

/* ===== EXPERIENCE STATS ===== */
.experience-stats {
    padding: 4rem 0;
    background-color: var(--white);
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.stat-card__icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.stat-card__icon i {
    font-size: 2rem;
    color: var(--white);
}

.stat-card__number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-card__label {
    color: var(--text-light);
    font-size: 1.125rem;
    margin: 0;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
    padding: 5rem 0;
    background-color: var(--bg-light);
}

.portfolio-filter {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.filter-btn {
    background: var(--white);
    border: 2px solid var(--border-color);
    color: var(--text-dark);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.portfolio-item {
    transition: var(--transition);
}

.portfolio-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.portfolio-card {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.portfolio-card__image {
    position: relative;
    overflow: hidden;
}

.portfolio-card__image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: var(--transition);
}

.portfolio-card:hover .portfolio-card__image img {
    transform: scale(1.1);
}

.portfolio-card__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(30, 58, 138, 0.9), rgba(59, 130, 246, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
}

.portfolio-card:hover .portfolio-card__overlay {
    opacity: 1;
}

.portfolio-card__content {
    text-align: center;
    color: var(--white);
    padding: 2rem;
}

.portfolio-card__content h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--white);
}

.portfolio-card__content p {
    margin-bottom: 1rem;
    opacity: 0.9;
}

.portfolio-card__category {
    background: var(--accent-color);
    color: var(--white);
    padding: 0.25rem 1rem;
    border-radius: 15px;
    font-size: 0.875rem;
    font-weight: 500;
}

/* ===== CERTIFICATIONS SECTION ===== */
.certifications {
    padding: 5rem 0;
    background-color: var(--white);
}

.cert-card {
    text-align: center;
    padding: 2rem;
    background: var(--bg-light);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    height: 100%;
}

.cert-card:hover {
    background: var(--white);
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.cert-card__icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--accent-color), #fb923c);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.cert-card__icon i {
    font-size: 2rem;
    color: var(--white);
}

.cert-card h4 {
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.cert-card p {
    color: var(--text-light);
    margin: 0;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    padding: 5rem 0;
    background: linear-gradient(135deg, var(--bg-dark) 0%, var(--primary-color) 100%);
    position: relative;
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    opacity: 0.1;
    z-index: 1;
}

.testimonials .container {
    position: relative;
    z-index: 2;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius-lg);
    padding: 2.5rem;
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
    transition: var(--transition);
    height: 100%;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.testimonial-card__content {
    margin-bottom: 2rem;
}

.testimonial-card__stars {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1.125rem;
}

.testimonial-card__content p {
    color: var(--text-light);
    font-style: italic;
    font-size: 1.125rem;
    line-height: 1.6;
    margin: 0;
}

.testimonial-card__author h5 {
    color: var(--text-dark);
    margin-bottom: 0.25rem;
    font-size: 1.125rem;
}

.testimonial-card__author span {
    color: var(--text-light);
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .portfolio-filter {
        justify-content: center;
    }

    .filter-btn {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
    }

    .testimonials::before {
        background-attachment: scroll;
    }
}
