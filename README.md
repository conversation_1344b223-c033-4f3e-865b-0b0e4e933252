# Dynasty Plumbing & Heating Website

A modern, professional website redesign for Dynasty Plumbing & Heating Ltd., a plumbing, heating, and gas fitting company serving the Greater Vancouver area.

## 🚀 Features

### Modern Design
- **Professional Color Scheme**: Deep blue primary (#1e3a8a), light blue secondary (#3b82f6), and orange accent (#f97316)
- **Typography**: Inter for body text and Playfair Display for headings
- **Text-based Logo**: Custom styled "Dynasty Plumbing & Heating" branding
- **Responsive Design**: Mobile-first approach with Bootstrap 5.3.2

### Technical Implementation
- **Semantic HTML5**: Proper use of header, nav, main, section, article, and footer elements
- **CSS Methodology**: BEM (Block Element Modifier) naming convention
- **Modern CSS**: CSS Grid, Flexbox, custom properties (CSS variables)
- **JavaScript**: Vanilla JS with modern ES6+ features
- **Performance Optimized**: Lazy loading, throttled scroll events, intersection observers

### SEO & Accessibility
- **Meta Tags**: Comprehensive title, description, and Open Graph tags
- **Structured Data**: JSON-LD format for LocalBusiness and FAQPage schemas
- **Semantic Markup**: Proper heading hierarchy (h1-h6)
- **Alt Attributes**: All images include descriptive alt text
- **ARIA Labels**: Accessibility improvements for screen readers

### Interactive Features
- **Parallax Effects**: Hero and contact section backgrounds
- **Smooth Scrolling**: Anchor link navigation
- **Animated Counters**: Statistics section with counting animations
- **Portfolio Filter**: Interactive project filtering by category
- **Form Validation**: Contact and newsletter forms with validation
- **Scroll Animations**: Elements animate into view on scroll

## 📁 Project Structure

```
dynastyplumbingheatinggas/
├── index.html              # Homepage
├── pricing.html            # Pricing information page
├── experience.html         # Portfolio and experience page
├── css/
│   └── style.css           # Main stylesheet (BEM methodology)
├── js/
│   ├── script.js           # Main JavaScript functionality
│   └── portfolio.js        # Portfolio filtering and animations
└── README.md               # Project documentation
```

## 🎨 Design System

### Colors
- **Primary**: #1e3a8a (Deep Blue)
- **Secondary**: #3b82f6 (Light Blue)
- **Accent**: #f97316 (Orange)
- **Text Dark**: #1f2937
- **Text Light**: #6b7280
- **Background Light**: #f8fafc
- **Background Dark**: #0f172a

### Typography
- **Primary Font**: Inter (Google Fonts)
- **Display Font**: Playfair Display (Google Fonts)
- **Font Sizes**: Responsive scaling with rem units

### Components
- **Service Cards**: Hover effects with icon gradients
- **Pricing Cards**: Featured card highlighting
- **Portfolio Items**: Image overlays with category filtering
- **Testimonials**: Glass-morphism effect cards
- **Contact Form**: Modern styling with focus states

## 🛠️ Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern features including Grid, Flexbox, Custom Properties
- **JavaScript (ES6+)**: Vanilla JS with modern syntax
- **Bootstrap 5.3.2**: Responsive grid system and components
- **Font Awesome 6.4.0**: Icon library
- **Google Fonts**: Inter and Playfair Display
- **Unsplash**: High-quality stock images

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 991px
- **Desktop**: 992px - 1199px
- **Large Desktop**: ≥ 1200px

## 🔧 Development

### Local Development Server
The project includes a PHP development server setup:

```bash
php -S localhost:9001
```

### File Organization
- **CSS**: BEM methodology for maintainable styles
- **JavaScript**: Modular approach with separate files for different functionality
- **Images**: External CDN links to Unsplash for placeholder images

## 📊 Performance Features

- **Lazy Loading**: Images load as they enter viewport
- **Throttled Events**: Scroll events optimized for 60fps
- **Intersection Observer**: Efficient scroll-based animations
- **Optimized Images**: WebP format support and responsive sizing
- **Minified Assets**: Production-ready CSS and JS

## 🎯 SEO Optimization

### On-Page SEO
- **Title Tags**: Unique, descriptive titles for each page
- **Meta Descriptions**: Compelling descriptions under 160 characters
- **Header Tags**: Proper H1-H6 hierarchy
- **Internal Linking**: Strategic anchor text linking

### Structured Data
- **LocalBusiness Schema**: Complete business information
- **FAQPage Schema**: Structured FAQ section
- **Service Schema**: Detailed service offerings

### Technical SEO
- **Mobile-First**: Responsive design prioritizing mobile experience
- **Page Speed**: Optimized loading times
- **Clean URLs**: SEO-friendly URL structure
- **Sitemap Ready**: Structure ready for XML sitemap generation

## 📞 Contact Information

- **Phone**: (*************
- **Service Area**: Greater Vancouver & Beyond
- **Emergency Service**: Available 24/7

## 🏆 Key Improvements from Original

1. **Modern Design**: Contemporary layout vs. outdated original
2. **Mobile Responsive**: Fully responsive vs. non-responsive original
3. **Performance**: Fast loading with optimized assets
4. **SEO**: Comprehensive SEO optimization
5. **User Experience**: Intuitive navigation and interactions
6. **Professional Branding**: Cohesive visual identity
7. **Content Organization**: Clear service categorization
8. **Call-to-Actions**: Strategic placement of contact information

## 🚀 Deployment Ready

The website is production-ready and can be deployed to any web server supporting:
- Static file hosting
- PHP (optional, for contact form processing)
- HTTPS (recommended for security)

---

**Built with modern web standards and best practices for Dynasty Plumbing & Heating Ltd.**
